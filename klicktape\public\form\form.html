<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Klicktape - Join the Waitlist</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Exo+2:wght@400;700&family=Space+Mono:wght@400;700&display=swap" rel="stylesheet" />
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              gold: '#FFD700',
              black: '#000000',
              crystal: '#1A1A33',
              prism: '#FF9500',
              lightgold: '#FFF8E1',
            },
            boxShadow: {
              prism: '0 0 30px rgba(255, 149, 0, 0.8), 0 0 60px rgba(255, 149, 0, 0.4)',
              gold: '0 0 25px rgba(255, 215, 0, 0.9)',
            },
            fontFamily: {
              exo: ['Exo 2', 'sans-serif'],
              space: ['Space Mono', 'monospace'],
            },
          },
        },
      };
    </script>
    <style>
      body {
        margin: 0;
        background: linear-gradient(to bottom, #000000, #1A1A33);
        color: #FFF8E1;
        font-family: 'Space Mono', monospace;
        overflow-x: hidden;
      }
      .prism-card {
        background: linear-gradient(135deg, rgba(26, 26, 51, 0.9), rgba(0, 0, 0, 0.7));
        border: 1px solid #FFD700;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }
      .prism-card:hover {
        transform: scale(1.02);
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
      }
      input, textarea {
        background: rgba(255, 248, 225, 0.1);
        border: 1px solid #FFD700;
        color: #FFF8E1;
        transition: border-color 0.3s ease;
      }
      input:focus, textarea:focus {
        border-color: #FF9500;
        outline: none;
        box-shadow: 0 0 10px rgba(255, 149, 0, 0.5);
      }
      label {
        color: #FFD700;
      }
      ::placeholder {
        color: #FFF8E1;
        opacity: 0.7;
      }
      .mobile-menu {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.95);
        z-index: 100;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transform: translateX(100%);
        transition: transform 0.3s ease;
      }
      .mobile-menu.active {
        transform: translateX(0);
      }
      .hamburger {
        display: block;
        font-size: 2rem;
        color: #FFD700;
        cursor: pointer;
      }
      @media (min-width: 768px) {
        .hamburger {
          display: none;
        }
        .mobile-menu {
          display: none;
        }
      }
    </style>
  </head>

  <body>
    <!-- Navbar -->
    <nav class="fixed top-0 w-full bg-black/90 backdrop-blur-2xl z-50 shadow-gold">
      <div class="max-w-7xl mx-auto px-4 py-4 flex justify-between items-center">
        <a href="../index.html" class="text-2xl sm:text-3xl font-exo text-gold">Klicktape</a>
        <div class="hidden md:flex space-x-8 text-sm font-space uppercase tracking-widest">
          <a href="../index.html#features" class="hover:text-prism transition-colors">Features</a>
          <a href="../index.html#about" class="hover:text-prism transition-colors">About</a>
          <a href="../index.html#how-it-works" class="hover:text-prism transition-colors">How It Works</a>
          <a href="../index.html#testimonials" class="hover:text-prism transition-colors">Testimonials</a>
          <a href="../index.html#faq" class="hover:text-prism transition-colors">FAQ</a>
        </div>
        <a href="../index.html" class="hidden md:block bg-gradient-to-r from-gold to-prism text-black px-4 py-2 rounded-full font-space hover:shadow-prism transition-all">Back</a>
        <div class="hamburger md:hidden">☰</div>
      </div>
      <!-- Mobile Menu -->
      <div class="mobile-menu">
        <div class="absolute top-4 right-4 text-3xl text-gold cursor-pointer close-menu">×</div>
        <a href="../index.html#features" class="text-2xl font-space text-lightgold py-4 hover:text-prism">Features</a>
        <a href="../index.html#about" class="text-2xl font-space text-lightgold py-4 hover:text-prism">About</a>
        <a href="../index.html#how-it-works" class="text-2xl font-space text-lightgold py-4 hover:text-prism">How It Works</a>
        <a href="../index.html#testimonials" class="text-2xl font-space text-lightgold py-4 hover:text-prism">Testimonials</a>
        <a href="../index.html#faq" class="text-2xl font-space text-lightgold py-4 hover:text-prism">FAQ</a>
        <a href="../index.html" class="bg-gradient-to-r from-gold to-prism text-black px-6 py-3 rounded-full font-space text-lg mt-6 hover:shadow-prism transition-all">Back</a>
      </div>
    </nav>

    <!-- Form Section -->
    <section class="max-w-4xl mx-auto px-4 py-12 mt-20">
      <div class="prism-card rounded-lg shadow-lg p-8">
        <h2 class="text-3xl sm:text-4xl font-exo text-center mb-8 text-gold">Join the Waitlist</h2>
        <form action="" id="contactForm" class="space-y-6">
          <!-- Name Field -->
          <div>
            <label for="name" class="block text-sm font-medium">Name</label>
            <input
              type="text"
              id="name"
              name="name"
              placeholder="Enter your name"
              class="mt-1 block w-full px-4 py-2 rounded-lg shadow-sm"
              required
            />
          </div>

          <!-- Email Field -->
          <div>
            <label for="email" class="block text-sm font-medium">Email</label>
            <input
              type="email"
              id="email"
              name="email"
              placeholder="Enter your email"
              class="mt-1 block w-full px-4 py-2 rounded-lg shadow-sm"
              required
            />
          </div>

          <!-- Phone Field -->
          <div>
            <label for="phone" class="block text-sm font-medium">Phone</label>
            <input
              type="tel"
              id="phone"
              name="phone"
              placeholder="Enter your phone number"
              class="mt-1 block w-full px-4 py-2 rounded-lg shadow-sm"
            />
          </div>

          <!-- Message Field -->
          <div>
            <label for="message" class="block text-sm font-medium">Message</label>
            <textarea
              id="message"
              name="message"
              rows="4"
              placeholder="Enter your message"
              class="mt-1 block w-full px-4 py-2 rounded-lg shadow-sm"
            ></textarea>
          </div>

          <!-- Submit Button -->
          <div>
            <button
              type="submit"
              class="w-full bg-gradient-to-r from-gold to-prism text-black px-6 py-3 rounded-lg font-space hover:shadow-gold transition-all"
            >
              Submit
            </button>
          </div>
        </form>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gradient-to-b from-black to-crystal py-16 border-t border-gold/30 relative overflow-hidden">
      <!-- Background Pattern -->
      <div class="absolute inset-0 opacity-10">
        <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-gold/20 via-transparent to-prism/20"></div>
        <div class="absolute top-10 left-10 w-32 h-32 border border-gold/30 rotate-45 rounded-lg"></div>
        <div class="absolute bottom-10 right-10 w-24 h-24 border border-prism/30 rotate-12 rounded-lg"></div>
      </div>

      <div class="max-w-7xl mx-auto px-4 relative z-10">
        <!-- Main Footer Content -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">

          <!-- Brand Section -->
          <div class="lg:col-span-1">
            <div class="mb-6">
              <a href="../index.html" class="text-3xl font-exo text-gold hover:text-prism transition-all duration-300 hover:shadow-gold">
                Klicktape
              </a>
              <div class="w-16 h-1 bg-gradient-to-r from-gold to-prism mt-2 rounded-full"></div>
            </div>
            <p class="text-lightgold/80 font-space text-sm leading-relaxed mb-4">
              Join the waitlist for the privacy-first social media platform that puts you in control.
            </p>
            <div class="flex space-x-4">
              <!-- LinkedIn -->
              <a href="https://www.linkedin.com/company/*********/" target="_blank" rel="noopener noreferrer" class="w-10 h-10 bg-gold/20 hover:bg-gold/40 rounded-full flex items-center justify-center transition-all duration-300 hover:shadow-gold group">
                <svg class="w-5 h-5 text-gold group-hover:text-black transition-colors" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
              <!-- X (Twitter) -->
              <a href="https://x.com/klicktape" target="_blank" rel="noopener noreferrer" class="w-10 h-10 bg-gold/20 hover:bg-gold/40 rounded-full flex items-center justify-center transition-all duration-300 hover:shadow-gold group">
                <svg class="w-5 h-5 text-gold group-hover:text-black transition-colors" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                </svg>
              </a>
              <!-- Instagram -->
              <a href="https://www.instagram.com/klicktape/" target="_blank" rel="noopener noreferrer" class="w-10 h-10 bg-gold/20 hover:bg-gold/40 rounded-full flex items-center justify-center transition-all duration-300 hover:shadow-gold group">
                <svg class="w-5 h-5 text-gold group-hover:text-black transition-colors" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                </svg>
              </a>
              <!-- Facebook -->
              <a href="https://www.facebook.com/profile.php?id=61568703147404" target="_blank" rel="noopener noreferrer" class="w-10 h-10 bg-gold/20 hover:bg-gold/40 rounded-full flex items-center justify-center transition-all duration-300 hover:shadow-gold group">
                <svg class="w-5 h-5 text-gold group-hover:text-black transition-colors" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </a>
            </div>
          </div>

          <!-- Quick Links -->
          <div>
            <h3 class="text-lg font-exo text-gold mb-4 relative">
              Quick Links
              <div class="w-8 h-0.5 bg-prism absolute -bottom-1 left-0"></div>
            </h3>
            <ul class="space-y-3 font-space text-sm">
              <li><a href="../index.html" class="text-lightgold/80 hover:text-gold transition-colors duration-300 hover:translate-x-1 inline-block">Home</a></li>
              <li><a href="../index.html#features" class="text-lightgold/80 hover:text-gold transition-colors duration-300 hover:translate-x-1 inline-block">Features</a></li>
              <li><a href="../index.html#about" class="text-lightgold/80 hover:text-gold transition-colors duration-300 hover:translate-x-1 inline-block">About Us</a></li>
              <li><a href="../index.html#faq" class="text-lightgold/80 hover:text-gold transition-colors duration-300 hover:translate-x-1 inline-block">FAQ</a></li>
              <li><a href="form.html" class="text-gold hover:text-prism transition-colors duration-300 hover:translate-x-1 inline-block font-bold">Join Waitlist</a></li>
            </ul>
          </div>

          <!-- Legal -->
          <div>
            <h3 class="text-lg font-exo text-gold mb-4 relative">
              Legal
              <div class="w-8 h-0.5 bg-prism absolute -bottom-1 left-0"></div>
            </h3>
            <ul class="space-y-3 font-space text-sm">
              <li><a href="../privacy-policy.html" class="text-lightgold/80 hover:text-gold transition-colors duration-300 hover:translate-x-1 inline-block">Privacy Policy</a></li>
              <li><a href="../terms-conditions.html" class="text-lightgold/80 hover:text-gold transition-colors duration-300 hover:translate-x-1 inline-block">Terms & Conditions</a></li>
              <li><a href="../child-safety-policy.html" class="text-lightgold/80 hover:text-gold transition-colors duration-300 hover:translate-x-1 inline-block">Child Safety Policy</a></li>
            </ul>
          </div>

          <!-- Contact & Support -->
          <div>
            <h3 class="text-lg font-exo text-gold mb-4 relative">
              Contact & Support
              <div class="w-8 h-0.5 bg-prism absolute -bottom-1 left-0"></div>
            </h3>
            <ul class="space-y-3 font-space text-sm">
              <li>
                <a href="mailto:<EMAIL>" class="text-lightgold/80 hover:text-gold transition-colors duration-300 flex items-center group">
                  <svg class="w-4 h-4 mr-2 group-hover:text-prism transition-colors" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                  </svg>
                  <EMAIL>
                </a>
              </li>
              <li>
                <a href="tel:+919678011096" class="text-lightgold/80 hover:text-gold transition-colors duration-300 flex items-center group">
                  <svg class="w-4 h-4 mr-2 group-hover:text-prism transition-colors" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                  </svg>
                  +91 9678011096
                </a>
              </li>
            </ul>
          </div>
        </div>

        <!-- Bottom Section -->
        <div class="border-t border-gold/20 pt-8">
          <div class="text-center">
            <p class="text-lightgold/60 font-space text-sm">
              © 2025 Klicktape Pvt Ltd. All rights reserved.
            </p>
            <p class="text-lightgold/40 font-space text-xs mt-1">
              Empowering privacy-first social connections worldwide
            </p>
          </div>
        </div>
      </div>
    </footer>

    <!-- Firebase v11 SDK -->
    <script type="module">
      import { initializeApp } from "https://www.gstatic.com/firebasejs/11.2.0/firebase-app.js";
      import {
        getDatabase,
        ref,
        push,
        serverTimestamp,
      } from "https://www.gstatic.com/firebasejs/11.2.0/firebase-database.js";

      const firebaseConfig = {
        apiKey: "AIzaSyBArk7fEX0Jgi26Phtp1t96XY-FxEtkN7M",
        authDomain: "klicktape-d087a.firebaseapp.com",
        databaseURL: "https://klicktape-d087a-default-rtdb.firebaseio.com/",
        projectId: "klicktape-d087a",
        storageBucket: "klicktape-d087a.firebasestorage.app",
        messagingSenderId: "537131358526",
        appId: "1:537131358526:web:22ba55670809a5b107746f",
      };

      const app = initializeApp(firebaseConfig);
      const database = getDatabase(app);

      document
        .getElementById("contactForm")
        .addEventListener("submit", function (event) {
          event.preventDefault();

          const name = document.getElementById("name").value;
          const email = document.getElementById("email").value;
          const phone = document.getElementById("phone").value;
          const message = document.getElementById("message").value;

          push(ref(database, "waitlist"), {
            name: name,
            email: email,
            phone: phone,
            message: message,
            timestamp: serverTimestamp(),
          })
            .then(() => {
              alert("Form submitted successfully!");
              document.getElementById("contactForm").reset();
            })
            .catch((error) => {
              console.error("Error submitting form: ", error);
              alert("There was an error submitting the form. Please try again.");
            });
        });

      // Mobile Menu Toggle
      const hamburger = document.querySelector('.hamburger');
      const mobileMenu = document.querySelector('.mobile-menu');
      const closeMenu = document.querySelector('.close-menu');
      hamburger.addEventListener('click', () => mobileMenu.classList.add('active'));
      closeMenu.addEventListener('click', () => mobileMenu.classList.remove('active'));
    </script>
  </body>
</html>