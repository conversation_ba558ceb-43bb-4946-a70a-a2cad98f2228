<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Klicktape</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #000000, #1a1a1a, #2a2a2a);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            max-width: 400px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            backdrop-filter: blur(10px);
        }
        .logo {
            font-size: 32px;
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 20px;
        }
        .message {
            font-size: 18px;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #FFD700;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            color: #ff6b6b;
            margin-top: 20px;
        }
        .manual-link {
            display: inline-block;
            margin-top: 20px;
            padding: 12px 24px;
            background: #FFD700;
            color: #000;
            text-decoration: none;
            border-radius: 8px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">Klicktape</div>
        <div class="message">
            Redirecting to the app to reset your password...
        </div>
        <div class="spinner"></div>
        <div id="error-message" class="error" style="display: none;">
            Unable to automatically redirect to the app.
            <br><br>
            <a href="#" id="manual-link" class="manual-link">Open Klicktape App</a>
        </div>
    </div>

    <script>
        function getUrlParams() {
            // Supabase sends tokens in URL hash
            const hashParams = new URLSearchParams(window.location.hash.substring(1));
            // Also check query params as fallback
            const queryParams = new URLSearchParams(window.location.search);

            return {
                access_token: hashParams.get('access_token') || queryParams.get('access_token'),
                refresh_token: hashParams.get('refresh_token') || queryParams.get('refresh_token'),
                error: hashParams.get('error') || queryParams.get('error'),
                error_description: hashParams.get('error_description') || queryParams.get('error_description'),
                type: hashParams.get('type') || queryParams.get('type')
            };
        }

        function redirectToApp() {
            const params = getUrlParams();
            console.log('URL params:', params);

            // Check for errors first
            if (params.error) {
                console.error('Auth error:', params.error, params.error_description);
                document.getElementById('error-message').style.display = 'block';
                document.querySelector('.spinner').style.display = 'none';
                document.querySelector('.message').textContent = 'Password reset link has expired or is invalid.';
                return;
            }

            // Check if this is a password recovery type or has access token
            if (params.access_token) {
                // Try multiple deep link formats for better compatibility
                const tokenParam = encodeURIComponent(params.access_token);
                const refreshParam = encodeURIComponent(params.refresh_token || '');

                // Universal link format (preferred)
                const universalUrl = `https://klicktape.com/reset-password?access_token=${tokenParam}&refresh_token=${refreshParam}`;

                // Custom scheme format (fallback)
                const customSchemeUrl = `klicktape://reset-password?access_token=${tokenParam}&refresh_token=${refreshParam}`;

                // Expo development format (for development)
                const expoUrl = `exp://**************:8081/--/reset-password?access_token=${tokenParam}&refresh_token=${refreshParam}`;

                console.log('Trying to redirect to app...');
                console.log('Universal URL:', universalUrl);
                console.log('Custom scheme:', customSchemeUrl);
                console.log('Expo URL:', expoUrl);

                // Try expo development first (for local development)
                window.location.href = expoUrl;

                // Set up manual links as fallback
                document.getElementById('manual-link').href = expoUrl;

                // Show manual option after 2 seconds if auto-redirect fails
                setTimeout(() => {
                    document.getElementById('error-message').style.display = 'block';
                    document.querySelector('.spinner').style.display = 'none';
                    document.querySelector('.message').textContent = 'If the app didn\'t open automatically, click below:';

                    // Add multiple options
                    const errorDiv = document.getElementById('error-message');
                    errorDiv.innerHTML = `
                        <div style="color: #FFD700; margin-bottom: 15px;">
                            If the app didn't open automatically, try one of these options:
                        </div>
                        <a href="${customSchemeUrl}" class="manual-link" style="display: block; margin: 10px 0;">
                            Open Klicktape App
                        </a>
                        <a href="${expoUrl}" class="manual-link" style="display: block; margin: 10px 0; background: #666;">
                            Open in Expo (Development)
                        </a>
                    `;
                }, 2000);
            } else {
                // No token found
                console.error('No access token found in URL');
                document.getElementById('error-message').style.display = 'block';
                document.querySelector('.spinner').style.display = 'none';
                document.querySelector('.message').textContent = 'Invalid password reset link. No access token found.';
            }
        }

        // Start the redirect process when page loads
        window.onload = redirectToApp;
    </script>
</body>
</html>
